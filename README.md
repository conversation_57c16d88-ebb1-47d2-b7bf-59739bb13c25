This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Features

- ✨ **Dark/Light Mode**: Complete theme switching with smooth transitions
- 🎨 **Modern Design**: Clean and professional landing page template
- 📱 **Responsive**: Optimized for all device sizes
- ⚡ **Fast**: Built with Next.js 15 and Tailwind CSS v4
- 🔧 **Customizable**: Easy to modify and extend

### Theme Switching

The application includes a complete dark/light mode implementation:

- **Theme Toggle**: Click the theme toggle button in the header
- **Keyboard Shortcut**: Press `Ctrl+Shift+T` (or `Cmd+Shift+T` on Mac) to toggle themes
- **System Preference**: Automatically detects and uses your system's theme preference
- **Persistence**: Remembers your theme choice across browser sessions
- **Smooth Transitions**: All theme changes include smooth CSS transitions

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load Inter and Poppins fonts with optimal performance.

## 📊 Optimization Status

**Optimization Score: 7.5/10** ⭐⭐⭐⭐⭐⭐⭐⚪⚪⚪

See [OPTIMIZATION_AUDIT_REPORT.md](./OPTIMIZATION_AUDIT_REPORT.md) for the full audit.

### ✅ Already Optimized
- Next.js 15 + React 19 (latest versions)
- TypeScript + ESLint (no errors/warnings)
- Font & Image optimization
- Complete SEO metadata with structured data
- Security headers and CSP
- Accessibility with ARIA labels
- Static generation for optimal performance
- Good bundle size (106 kB First Load JS)

### 🔄 Perlu Diperbaiki (Priority)
- [ ] **CRITICAL:** Tambahkan og-image.jpg (1200x630px)
- [ ] **CRITICAL:** Perbaiki CSP security policy
- [ ] **HIGH:** Implementasi React Error Boundaries
- [ ] **HIGH:** Setup bundle analyzer
- [ ] **MEDIUM:** Testing framework (Jest + RTL)
- [ ] **MEDIUM:** Performance monitoring

## 🛠️ Development Commands

```bash
# Development
npm run dev          # Start dev server
npm run build        # Build for production
npm start           # Start production server
npm run lint        # Run ESLint

# Analysis (setelah setup bundle analyzer)
ANALYZE=true npm run build
```

## 📁 Project Structure

```
src/
├── app/                 # Next.js App Router
│   ├── layout.tsx      # Root layout with metadata
│   ├── page.tsx        # Homepage
│   ├── globals.css     # Global styles & theme
│   └── robots.ts       # SEO robots
├── components/         # React components
│   ├── sections/       # Page sections (Hero, About, etc.)
│   ├── Container.tsx   # Layout container
│   ├── ThemeToggle.tsx # Theme switcher
│   └── StructuredData.tsx # SEO structured data
└── contexts/           # React contexts
    └── ThemeContext.tsx # Theme management
```

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
