import React from "react";
import Container from "../Container";

export function Contact() {
  return (
    <section className="py-16 sm:py-20">
      <Container>
        <h2 className="text-2xl sm:text-3xl font-heading font-semibold">Contact Us</h2>
        <div className="mt-6 grid gap-6 sm:grid-cols-2">
          <div className="rounded-xl border p-6">
            <h3 className="font-heading font-semibold">Contact Information</h3>
            <div className="mt-4 space-y-3 text-sm text-muted-foreground">
              <div>
                <strong>Email:</strong> <EMAIL>
              </div>
              <div>
                <strong>Phone:</strong> +62 ************
              </div>
              <div>
                <strong>WhatsApp:</strong> +62 ************
              </div>
            </div>
          </div>
          <div className="rounded-xl border p-6">
            <h3 className="font-heading font-semibold">Send a Message</h3>
            <form className="mt-4 space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium">
                  Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800"
                  placeholder="Your Name"
                />
              </div>
              <div>
                <label htmlFor="email" className="block text-sm font-medium">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800"
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label htmlFor="message" className="block text-sm font-medium">
                  Message
                </label>
                <textarea
                  id="message"
                  name="message"
                  rows={4}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800"
                  placeholder="Write your message here..."
                />
              </div>
              <button
                type="submit"
                className="inline-flex h-10 items-center justify-center rounded-md bg-primary-button-bg text-primary-button-text px-4 text-sm font-medium hover:bg-primary-button-hover transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-primary-button-bg"
              >
                Send Message
              </button>
            </form>
          </div>
        </div>
      </Container>
    </section>
  );
}
